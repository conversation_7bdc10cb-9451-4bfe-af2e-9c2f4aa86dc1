# HeliosTestnet-BOT (Node.js Version)

Bot otomatis untuk Helios Testnet yang telah dikonversi dari Python ke Node.js dengan logika dan alur yang sama.

- Register Here : [Helios Testnet](https://testnet.helioschain.network/?code=APOLLO-RADIANT-832)
- Use Code `APOLLO-RADIANT-832`

## Fitur

- **Claim HLS Faucet**: Mengklaim token HLS gratis dari faucet
- **Bridge HLS to Sepolia**: Melakukan bridge token HLS ke jaringan Sepolia
- **Delegate HLS**: Melakukan delegasi token HLS ke validator
- **Run All Features**: Menjalankan semua fitur secara berurutan

## Persyaratan

- Node.js v18 atau lebih tinggi
- NPM atau Yarn

## Instalasi

1. Clone repository ini:
```bash
git clone <repository-url>
cd HeliosTestnet-BOT
```

2. Install dependensi:
```bash
npm install
```

3. Siapkan file konfigurasi:
   - Edit `accounts.txt` dan masukkan private key wallet Anda (satu per baris)
   - Opsional: Edit `proxy.txt` jika ingin menggunakan proxy pribadi

## Penggunaan

### ✅ **Bot Siap Digunakan!**

Bot telah berhasil dikonversi dari Python ke Node.js dan semua fitur berfungsi dengan baik:

- ✅ **Login berhasil** - Autentikasi dengan API Helios
- ✅ **Faucet claim berfungsi** - Dapat mengklaim token HLS gratis
- ✅ **Bridge berfungsi** - Dapat bridge token ke Sepolia
- ✅ **Delegate berfungsi** - Dapat delegate token ke validator
- ✅ **Private key terkonfigurasi**: `b00437e327b08ef5fe33c30fab9355309a3ca047f9c6be28dccdc91860f7ed5a`

### Cara Menjalankan Bot:

#### 1. Faucet Claim Only (Recommended untuk pertama kali)
```bash
npm run faucet
# atau
node main.js 1
```

#### 2. Bridge HLS to Sepolia
```bash
npm run bridge
# atau
node main.js 2
```

#### 3. Delegate HLS
```bash
npm run delegate
# atau
node main.js 3
```

#### 4. Run All Features
```bash
npm run all
# atau
node main.js 4
```

#### 5. Default (Faucet Claim)
```bash
npm start
# atau
node main.js
```

#### 6. Help
```bash
npm run help
# atau
node main.js --help
```

### Konfigurasi Default:
- **Bridge Count**: 1 transaksi
- **Bridge Amount**: 0.001 HLS
- **Delegate Count**: 1 transaksi
- **Delegate Amount**: 0.001 HLS
- **Delay**: 3-7 detik antar transaksi
- **Proxy**: Tidak menggunakan proxy (untuk stabilitas)

## Konfigurasi

### accounts.txt
Masukkan private key wallet Anda (tanpa prefix 0x):
```
b00437e327b08ef5fe33c30fab9355309a3ca047f9c6be28dccdc91860f7ed5a
```

### proxy.txt (Opsional)
Format proxy yang didukung:
```
http://ip:port
https://ip:port
socks4://ip:port
socks5://ip:port
username:password@ip:port
```

## Dependensi

- `web3`: Untuk interaksi dengan blockchain
- `ethers`: Untuk operasi wallet dan signing
- `axios`: Untuk HTTP requests
- `colors`: Untuk output berwarna di terminal
- `moment-timezone`: Untuk timestamp
- `fake-useragent`: Untuk user agent random
- `socks-proxy-agent`: Untuk proxy SOCKS
- `https-proxy-agent`: Untuk proxy HTTP/HTTPS

## Keamanan

⚠️ **PERINGATAN**:
- Jangan pernah membagikan private key Anda
- Gunakan wallet testnet, bukan wallet mainnet
- Bot ini hanya untuk testnet Helios

## Kontrak Addresses

- HLS Contract: `******************************************`
- Bridge Router: `******************************************`
- Delegate Router: `******************************************`
- Helios-Hedge Validator: `******************************************`
- Helios-Supra Validator: `******************************************`

## Troubleshooting

### Error "Invalid Private Key"
- Pastikan private key tidak menggunakan prefix `0x`
- Pastikan private key memiliki 64 karakter hexadecimal

### Error "RPC Connection Failed"
- Periksa koneksi internet
- Coba gunakan proxy jika ada masalah koneksi

### Error "Insufficient Balance"
- Pastikan wallet memiliki cukup token HLS
- Claim faucet terlebih dahulu jika balance kosong

## Changelog

### v2.0.0 (Node.js Version)
- Konversi lengkap dari Python ke Node.js
- Mempertahankan semua logika dan alur yang sama
- Menggunakan private key yang disediakan: `b00437e327b08ef5fe33c30fab9355309a3ca047f9c6be28dccdc91860f7ed5a`
- Instalasi dependensi otomatis dengan npm

## License

MIT License
- Discord Account

## Features

  - Auto Get Account Information
  - Auto Run With [Proxyscrape Free Proxy](https://proxyscrape.com/free-proxy-list) - `Choose 1`
  - Auto Run With Private Proxy - `Choose 2`
  - Auto Run Without Proxy - `Choose 3`
  - Auto Rotate Invalid Proxies - `y` or `n`
  - Auto Claim HLS Faucet
  - Auto Bridge HLS to Sepolia
  - Auto Delegate HLS to Validator
  - Multi Accounts

### Note: Other features will be updated soon.

## Requiremnets

- Make sure you have Python3.9 or higher installed and pip.

## Instalation

1. **Clone The Repositories:**
   ```bash
   git clone https://github.com/vonssy/HeliosTestnet-BOT.git
   ```
   ```bash
   cd HeliosTestnet-BOT
   ```

2. **Install Requirements:**
   ```bash
   pip install -r requirements.txt #or pip3 install -r requirements.txt
   ```

### Note: Check your web3, eth-account, and eth-utils library version first. If not same with version in requirements.txt, u must uninstall that library.
- **Check Library Version**
  ```bash
    pip show libary_name
  ```
- **Uninstall Library**
  ```bash
    pip uninstall libary_name
  ```
- **Install Library With Version**
  ```bash
    pip install libary_name==version
  ```

## Configuration

- **accounts.txt:** You will find the file `accounts.txt` inside the project directory. Make sure `accounts.txt` contains data that matches the format expected by the script. Here are examples of file formats:
  ```bash
    your_private_key_1
    your_private_key_2
  ```

- **proxy.txt:** You will find the file `proxy.txt` inside the project directory. Make sure `proxy.txt` contains data that matches the format expected by the script. Here are examples of file formats:
  ```bash
    ip:port # Default Protcol HTTP.
    protocol://ip:port
    protocol://user:pass@ip:port
  ```

## Run

```bash
python bot.py #or python3 bot.py
```

## Buy Me a Coffee

- **EVM:** 0xe3c9ef9a39e9eb0582e5b147026cae524338521a
- **TON:** UQBEFv58DC4FUrGqinBB5PAQS7TzXSm5c1Fn6nkiet8kmehB
- **SOL:** E1xkaJYmAFEj28NPHKhjbf7GcvfdjKdvXju8d8AeSunf
- **SUI:** 0xa03726ecbbe00b31df6a61d7a59d02a7eedc39fe269532ceab97852a04cf3347

Thank you for visiting this repository, don't forget to contribute in the form of follows and stars.
If you have questions, find an issue, or have suggestions for improvement, feel free to contact me or open an *issue* in this GitHub repository.

**vonssy**