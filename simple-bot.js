import Helios from './bot.js';
import colors from 'colors';

async function runBot() {
    console.log('🚀 Starting Helios Testnet Bot (Node.js Version)');
    console.log('================================================');
    console.log('');
    console.log('Private Key: b00437e327b08ef5fe33c30fab9355309a3ca047f9c6be28dccdc91860f7ed5a');
    console.log('');
    
    try {
        const bot = new Helios();
        
        // Read accounts from file
        const fs = await import('fs');
        let accounts;
        try {
            const content = fs.readFileSync('accounts.txt', 'utf8');
            accounts = content.split('\n').filter(line => line.trim());
        } catch (error) {
            console.log(`${colors.red.bold('File \'accounts.txt\' Not Found.')}`);
            return;
        }

        console.log(`${colors.green.bold('Account\'s Total:')} ${colors.white.bold(accounts.length)}`);
        console.log('');
        
        // Set default options for testing
        const option = 1; // Claim HLS Faucet
        const useProxy = false; // No proxy
        const rotateProxy = false;
        
        console.log(`${colors.green.bold('Selected Option:')} ${colors.white.bold('1. Claim HLS Faucet')}`);
        console.log(`${colors.green.bold('Proxy Mode:')} ${colors.white.bold('No Proxy')}`);
        console.log('');
        
        // Clear terminal and show welcome
        bot.clearTerminal();
        bot.welcome();
        
        const separator = "=".repeat(25);
        for (const account of accounts) {
            if (account) {
                const address = bot.generateAddress(account);

                console.log(`${colors.cyan.bold(separator)}${colors.cyan.bold('[')}${colors.white.bold(` ${bot.maskAccount(address)} `)}${colors.cyan.bold(']')}${colors.cyan.bold(separator)}`);

                if (!address) {
                    console.log(`${colors.cyan.bold('Status    :')} ${colors.red.bold('Invalid Private Key or Library Version Not Supported')}`);
                    continue;
                }

                // Process login and faucet claim
                const logined = await bot.processUserLogin(account, address, useProxy, rotateProxy);
                if (logined) {
                    await bot.processOption1(address, useProxy);
                }
                
                await bot.sleep(3000);
            }
        }
        
        console.log(`${colors.cyan.bold('=')}`.repeat(72));
        console.log(`${colors.green.bold('✅ All accounts processed successfully!')}`);
        
    } catch (error) {
        console.error('Error occurred:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

runBot();
