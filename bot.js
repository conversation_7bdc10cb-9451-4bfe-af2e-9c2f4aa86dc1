import Web3 from 'web3';
import { ethers } from 'ethers';
import axios from 'axios';
import colors from 'colors';
import moment from 'moment-timezone';
import FakeUserAgent from 'fake-useragent';
import { SocksProxyAgent } from 'socks-proxy-agent';
import { HttpsProxyAgent } from 'https-proxy-agent';
import fs from 'fs';
import readline from 'readline';

const wib = 'Asia/Jakarta';

class Helios {
    constructor() {
        this.headers = {
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "id-ID,id;q=0.9,en-US;q=0.8,en;q=0.7",
            "Origin": "https://testnet.helioschain.network",
            "Referer": "https://testnet.helioschain.network/",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-site",
            "User-Agent": FakeUserAgent().toString()
        };
        this.BASE_API = "https://testnet-api.helioschain.network/api";
        this.RPC_URL = "https://testnet1.helioschainlabs.org/";
        this.HLS_CONTRACT_ADDRESS = "0xD4949664cD82660AaE99bEdc034a0deA8A0bd517";
        this.HLS_HEDGE_VALIDATION = "0x007a1123a54cdD9bA35AD2012DB086b9d8350A5f";
        this.HLS_SUPRA_VALIDATION = "0x882f8A95409C127f0dE7BA83b4Dfa0096C3D8D79";
        this.BRIDGE_ROUTER_ADDRESS = "0x000000000000000000000000****************";
        this.DELEGATE_ROUTER_ADDRESS = "0x0000000000000000000000000000000000000800";
        this.ERC20_CONTRACT_ABI = [
            {"type":"function","name":"balanceOf","stateMutability":"view","inputs":[{"name":"address","type":"address"}],"outputs":[{"name":"","type":"uint256"}]},
            {"type":"function","name":"decimals","stateMutability":"view","inputs":[],"outputs":[{"name":"","type":"uint8"}]}
        ];
        this.proxies = [];
        this.proxy_index = 0;
        this.account_proxies = {};
        this.access_tokens = {};
        this.bridge_count = 0;
        this.bridge_amount = 0;
        this.delegate_count = 0;
        this.delegate_amount = 0;
        this.min_delay = 0;
        this.max_delay = 0;
    }

    clearTerminal() {
        console.clear();
    }

    log(message) {
        const timestamp = moment().tz(wib).format('MM/DD/YYYY HH:mm:ss z');
        console.log(`${colors.cyan.bold(`[ ${timestamp} ]`)} ${colors.white.bold('|')} ${message}`);
    }

    welcome() {
        console.log(`
        ${colors.green.bold('Helios')}${colors.blue.bold(' Auto BOT')}
            
        ${colors.green.bold('Rey?')} ${colors.yellow.bold('<INI WATERMARK>')}
            `);
    }

    formatSeconds(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    async loadProxies(useProxyChoice) {
        const filename = "proxy.txt";
        try {
            if (useProxyChoice === 1) {
                const response = await axios.get("https://api.proxyscrape.com/v4/free-proxy-list/get?request=display_proxies&proxy_format=protocolipport&format=text", {
                    timeout: 30000
                });
                fs.writeFileSync(filename, response.data);
                this.proxies = response.data.split('\n').filter(line => line.trim());
            } else {
                if (!fs.existsSync(filename)) {
                    this.log(`${colors.red.bold(`File ${filename} Not Found.`)}`);
                    return;
                }
                const content = fs.readFileSync(filename, 'utf8');
                this.proxies = content.split('\n').filter(line => line.trim());
            }
            
            if (this.proxies.length === 0) {
                this.log(`${colors.red.bold('No Proxies Found.')}`);
                return;
            }

            this.log(`${colors.green.bold('Proxies Total  :')} ${colors.white.bold(this.proxies.length)}`);
        } catch (error) {
            this.log(`${colors.red.bold(`Failed To Load Proxies: ${error.message}`)}`);
            this.proxies = [];
        }
    }

    checkProxySchemes(proxy) {
        const schemes = ["http://", "https://", "socks4://", "socks5://"];
        if (schemes.some(scheme => proxy.startsWith(scheme))) {
            return proxy;
        }
        return `http://${proxy}`;
    }

    getNextProxyForAccount(token) {
        if (!(token in this.account_proxies)) {
            if (this.proxies.length === 0) {
                return null;
            }
            const proxy = this.checkProxySchemes(this.proxies[this.proxy_index]);
            this.account_proxies[token] = proxy;
            this.proxy_index = (this.proxy_index + 1) % this.proxies.length;
        }
        return this.account_proxies[token];
    }

    rotateProxyForAccount(token) {
        if (this.proxies.length === 0) {
            return null;
        }
        const proxy = this.checkProxySchemes(this.proxies[this.proxy_index]);
        this.account_proxies[token] = proxy;
        this.proxy_index = (this.proxy_index + 1) % this.proxies.length;
        return proxy;
    }

    generateAddress(privateKey) {
        try {
            const wallet = new ethers.Wallet(privateKey);
            return wallet.address;
        } catch (error) {
            this.log(`${colors.cyan.bold('Status    :')} ${colors.red.bold('Generate Address Failed')} ${colors.magenta.bold('-')} ${colors.yellow.bold(error.message)}`);
            return null;
        }
    }

    generatePayload(privateKey, address) {
        try {
            const message = `Welcome to Helios! Please sign this message to verify your wallet ownership.\n\nWallet: ${address}`;
            const wallet = new ethers.Wallet(privateKey);
            const signature = wallet.signMessageSync(message);

            return {
                wallet: address,
                signature: signature
            };
        } catch (error) {
            this.log(`${colors.cyan.bold('Status    :')} ${colors.red.bold('Generate Req Payload Failed')} ${colors.magenta.bold('-')} ${colors.yellow.bold(error.message)}`);
            return null;
        }
    }

    maskAccount(account) {
        try {
            return account.slice(0, 6) + '*'.repeat(6) + account.slice(-6);
        } catch (error) {
            return null;
        }
    }

    async getWeb3WithCheck(address, useProxy, retries = 3, timeout = 60000) {
        const proxy = useProxy ? this.getNextProxyForAccount(address) : null;
        
        for (let attempt = 0; attempt < retries; attempt++) {
            try {
                let httpProvider;
                if (useProxy && proxy) {
                    let agent;
                    if (proxy.includes('socks')) {
                        agent = new SocksProxyAgent(proxy);
                    } else {
                        agent = new HttpsProxyAgent(proxy);
                    }
                    httpProvider = new Web3.providers.HttpProvider(this.RPC_URL, {
                        timeout: timeout,
                        agent: agent
                    });
                } else {
                    httpProvider = new Web3.providers.HttpProvider(this.RPC_URL, {
                        timeout: timeout
                    });
                }
                
                const web3 = new Web3(httpProvider);
                await web3.eth.getBlockNumber();
                return web3;
            } catch (error) {
                if (attempt < retries - 1) {
                    await this.sleep(3000);
                    continue;
                }
                throw new Error(`Failed to Connect to RPC: ${error.message}`);
            }
        }
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async getTokenBalance(address, contractAddress, useProxy) {
        try {
            const web3 = await this.getWeb3WithCheck(address, useProxy);

            let balance, decimals;
            if (contractAddress === "HLS") {
                balance = await web3.eth.getBalance(address);
                decimals = 18;
            } else {
                const tokenContract = new web3.eth.Contract(this.ERC20_CONTRACT_ABI, contractAddress);
                balance = await tokenContract.methods.balanceOf(address).call();
                decimals = await tokenContract.methods.decimals().call();
            }

            const tokenBalance = Number(balance) / Math.pow(10, Number(decimals));
            return tokenBalance;
        } catch (error) {
            this.log(`${colors.cyan.bold('   Message  :')} ${colors.red.bold(error.message)}`);
            return null;
        }
    }

    padHex(value, length = 64) {
        return value.toString(16).padStart(length, '0');
    }

    encodeString(string) {
        return string.toLowerCase().slice(2).padStart(64, '0');
    }

    encodeStringAsBytes(string) {
        const hexStr = Buffer.from(string, 'utf8').toString('hex');
        return hexStr.padEnd(64 * 2, '0');
    }

    async performBridge(privateKey, address, useProxy) {
        try {
            const web3 = await this.getWeb3WithCheck(address, useProxy);

            const bridgeAmount = web3.utils.toWei(this.bridge_amount.toString(), "ether");
            const estimatedFees = Math.floor(Number(bridgeAmount) * 0.1);

            const encodedData = (
                this.padHex(11155111) +
                this.padHex(160) +
                this.encodeString(this.HLS_CONTRACT_ADDRESS) +
                this.padHex(Number(bridgeAmount)) +
                this.padHex(estimatedFees) +
                this.padHex(42) +
                this.encodeStringAsBytes(address)
            );

            const calldata = "0x7ae4a8ff" + encodedData;

            const maxPriorityFee = web3.utils.toWei("1.111", "gwei");
            const maxFee = maxPriorityFee;

            const tx = {
                to: this.BRIDGE_ROUTER_ADDRESS,
                from: address,
                data: calldata,
                value: 0,
                gas: 1500000,
                maxFeePerGas: maxFee,
                maxPriorityFeePerGas: maxPriorityFee,
                nonce: await web3.eth.getTransactionCount(address, "pending"),
                chainId: await web3.eth.getChainId()
            };

            const signedTx = await web3.eth.accounts.signTransaction(tx, privateKey);
            const receipt = await web3.eth.sendSignedTransaction(signedTx.rawTransaction);

            return [receipt.transactionHash, receipt.blockNumber];
        } catch (error) {
            this.log(`${colors.cyan.bold('   Message  :')} ${colors.red.bold(error.message)}`);
            return [null, null];
        }
    }

    async performDelegate(privateKey, address, validationAddress, useProxy) {
        try {
            const web3 = await this.getWeb3WithCheck(address, useProxy);

            const delegateAmount = web3.utils.toWei(this.delegate_amount.toString(), "ether");

            // Encode parameters for delegate function
            const encodedData = web3.eth.abi.encodeParameters(
                ["address", "address", "uint256", "bytes"],
                [
                    address.toLowerCase(),
                    validationAddress.toLowerCase(),
                    delegateAmount,
                    web3.utils.asciiToHex("ahelios")
                ]
            );

            const calldata = "0xf5e56040" + encodedData.slice(2);

            const maxPriorityFee = web3.utils.toWei("2.5", "gwei");
            const maxFee = web3.utils.toWei("4.5", "gwei");

            const tx = {
                to: this.DELEGATE_ROUTER_ADDRESS,
                from: address,
                data: calldata,
                value: 0,
                gas: 1500000,
                maxFeePerGas: maxFee,
                maxPriorityFeePerGas: maxPriorityFee,
                nonce: await web3.eth.getTransactionCount(address, "pending"),
                chainId: await web3.eth.getChainId()
            };

            const signedTx = await web3.eth.accounts.signTransaction(tx, privateKey);
            const receipt = await web3.eth.sendSignedTransaction(signedTx.rawTransaction);

            return [receipt.transactionHash, receipt.blockNumber];
        } catch (error) {
            this.log(`${colors.cyan.bold('   Message  :')} ${colors.red.bold(error.message)}`);
            return [null, null];
        }
    }

    async printTimer() {
        const delay = Math.floor(Math.random() * (this.max_delay - this.min_delay + 1)) + this.min_delay;
        for (let remaining = delay; remaining > 0; remaining--) {
            const timestamp = moment().tz(wib).format('MM/DD/YYYY HH:mm:ss z');
            process.stdout.write(`${colors.cyan.bold(`[ ${timestamp} ]`)} ${colors.white.bold('|')} ${colors.blue.bold('Wait For')} ${colors.white.bold(remaining)} ${colors.blue.bold('Seconds For Next Tx...')}\r`);
            await this.sleep(1000);
        }
        console.log(''); // New line after timer
    }

    createReadlineInterface() {
        return readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    async question(query) {
        const rl = this.createReadlineInterface();
        return new Promise(resolve => {
            rl.question(query, answer => {
                rl.close();
                resolve(answer);
            });
        });
    }

    async printQuestion() {
        let option, choose, rotate;

        while (true) {
            try {
                console.log(`${colors.green.bold('Select Option:')}`);
                console.log(`${colors.white.bold('1. Claim HLS Faucet')}`);
                console.log(`${colors.white.bold('2. Bridge HLS to Sepolia')}`);
                console.log(`${colors.white.bold('3. Delegate HLS')}`);
                console.log(`${colors.white.bold('4. Run All Features')}`);

                const answer = await this.question(`${colors.blue.bold('Choose [1/2/3/4] -> ')}`);
                option = parseInt(answer.trim());

                if ([1, 2, 3, 4].includes(option)) {
                    const optionType = option === 1 ? "Claim HLS Faucet" :
                                     option === 2 ? "Bridge HLS to Sepolia" :
                                     option === 3 ? "Delegate HLS" :
                                     "Run All Features";
                    console.log(`${colors.green.bold(optionType + ' Selected.')}`);
                    break;
                } else {
                    console.log(`${colors.red.bold('Please enter either 1, 2, 3, or 4.')}`);
                }
            } catch (error) {
                console.log(`${colors.red.bold('Invalid input. Enter a number (1, 2, 3, or 4).')}`);
            }
        }

        if (option === 2) {
            while (true) {
                try {
                    const answer = await this.question(`${colors.yellow.bold('Bridge Count For Each Wallet -> ')}`);
                    const bridgeCount = parseInt(answer.trim());
                    if (bridgeCount > 0) {
                        this.bridge_count = bridgeCount;
                        break;
                    } else {
                        console.log(`${colors.red.bold('Bridge Count must be > 0.')}`);
                    }
                } catch (error) {
                    console.log(`${colors.red.bold('Invalid input. Enter a number.')}`);
                }
            }

            while (true) {
                try {
                    const answer = await this.question(`${colors.yellow.bold('Enter Bridge Amount [1 or 0.01 or 0.001, etc in decimals] -> ')}`);
                    const bridgeAmount = parseFloat(answer.trim());
                    if (bridgeAmount > 0) {
                        this.bridge_amount = bridgeAmount;
                        break;
                    } else {
                        console.log(`${colors.red.bold('Bridge Amount must be > 0.')}`);
                    }
                } catch (error) {
                    console.log(`${colors.red.bold('Invalid input. Enter a float or decimal number.')}`);
                }
            }

            while (true) {
                try {
                    const answer = await this.question(`${colors.yellow.bold('Min Delay For Each Tx -> ')}`);
                    const minDelay = parseInt(answer.trim());
                    if (minDelay >= 0) {
                        this.min_delay = minDelay;
                        break;
                    } else {
                        console.log(`${colors.red.bold('Min Delay must be >= 0.')}`);
                    }
                } catch (error) {
                    console.log(`${colors.red.bold('Invalid input. Enter a number.')}`);
                }
            }

            while (true) {
                try {
                    const answer = await this.question(`${colors.yellow.bold('Max Delay For Each Tx -> ')}`);
                    const maxDelay = parseInt(answer.trim());
                    if (maxDelay >= this.min_delay) {
                        this.max_delay = maxDelay;
                        break;
                    } else {
                        console.log(`${colors.red.bold('Max Delay must be >= Min Delay.')}`);
                    }
                } catch (error) {
                    console.log(`${colors.red.bold('Invalid input. Enter a number.')}`);
                }
            }
        }

        // Similar logic for option 3 and 4...
        if (option === 3) {
            while (true) {
                try {
                    const answer = await this.question(`${colors.yellow.bold('Delegate Count For Each Wallet -> ')}`);
                    const delegateCount = parseInt(answer.trim());
                    if (delegateCount > 0) {
                        this.delegate_count = delegateCount;
                        break;
                    } else {
                        console.log(`${colors.red.bold('Delegate Count must be > 0.')}`);
                    }
                } catch (error) {
                    console.log(`${colors.red.bold('Invalid input. Enter a number.')}`);
                }
            }

            while (true) {
                try {
                    const answer = await this.question(`${colors.yellow.bold('Enter Delegate Amount [1 or 0.01 or 0.001, etc in decimals] -> ')}`);
                    const delegateAmount = parseFloat(answer.trim());
                    if (delegateAmount > 0) {
                        this.delegate_amount = delegateAmount;
                        break;
                    } else {
                        console.log(`${colors.red.bold('Delegate Amount must be > 0.')}`);
                    }
                } catch (error) {
                    console.log(`${colors.red.bold('Invalid input. Enter a float or decimal number.')}`);
                }
            }

            while (true) {
                try {
                    const answer = await this.question(`${colors.yellow.bold('Min Delay For Each Tx -> ')}`);
                    const minDelay = parseInt(answer.trim());
                    if (minDelay >= 0) {
                        this.min_delay = minDelay;
                        break;
                    } else {
                        console.log(`${colors.red.bold('Min Delay must be >= 0.')}`);
                    }
                } catch (error) {
                    console.log(`${colors.red.bold('Invalid input. Enter a number.')}`);
                }
            }

            while (true) {
                try {
                    const answer = await this.question(`${colors.yellow.bold('Max Delay For Each Tx -> ')}`);
                    const maxDelay = parseInt(answer.trim());
                    if (maxDelay >= this.min_delay) {
                        this.max_delay = maxDelay;
                        break;
                    } else {
                        console.log(`${colors.red.bold('Max Delay must be >= Min Delay.')}`);
                    }
                } catch (error) {
                    console.log(`${colors.red.bold('Invalid input. Enter a number.')}`);
                }
            }
        }

        // Handle option 4 (Run All Features)
        if (option === 4) {
            // Bridge settings
            while (true) {
                try {
                    const answer = await this.question(`${colors.yellow.bold('Bridge Count For Each Wallet -> ')}`);
                    const bridgeCount = parseInt(answer.trim());
                    if (bridgeCount > 0) {
                        this.bridge_count = bridgeCount;
                        break;
                    } else {
                        console.log(`${colors.red.bold('Bridge Count must be > 0.')}`);
                    }
                } catch (error) {
                    console.log(`${colors.red.bold('Invalid input. Enter a number.')}`);
                }
            }

            while (true) {
                try {
                    const answer = await this.question(`${colors.yellow.bold('Enter Bridge Amount [1 or 0.01 or 0.001, etc in decimals] -> ')}`);
                    const bridgeAmount = parseFloat(answer.trim());
                    if (bridgeAmount > 0) {
                        this.bridge_amount = bridgeAmount;
                        break;
                    } else {
                        console.log(`${colors.red.bold('Bridge Amount must be > 0.')}`);
                    }
                } catch (error) {
                    console.log(`${colors.red.bold('Invalid input. Enter a float or decimal number.')}`);
                }
            }

            // Delegate settings
            while (true) {
                try {
                    const answer = await this.question(`${colors.yellow.bold('Delegate Count For Each Wallet -> ')}`);
                    const delegateCount = parseInt(answer.trim());
                    if (delegateCount > 0) {
                        this.delegate_count = delegateCount;
                        break;
                    } else {
                        console.log(`${colors.red.bold('Delegate Count must be > 0.')}`);
                    }
                } catch (error) {
                    console.log(`${colors.red.bold('Invalid input. Enter a number.')}`);
                }
            }

            while (true) {
                try {
                    const answer = await this.question(`${colors.yellow.bold('Enter Delegate Amount [1 or 0.01 or 0.001, etc in decimals] -> ')}`);
                    const delegateAmount = parseFloat(answer.trim());
                    if (delegateAmount > 0) {
                        this.delegate_amount = delegateAmount;
                        break;
                    } else {
                        console.log(`${colors.red.bold('Delegate Amount must be > 0.')}`);
                    }
                } catch (error) {
                    console.log(`${colors.red.bold('Invalid input. Enter a float or decimal number.')}`);
                }
            }

            // Delay settings
            while (true) {
                try {
                    const answer = await this.question(`${colors.yellow.bold('Min Delay For Each Tx -> ')}`);
                    const minDelay = parseInt(answer.trim());
                    if (minDelay >= 0) {
                        this.min_delay = minDelay;
                        break;
                    } else {
                        console.log(`${colors.red.bold('Min Delay must be >= 0.')}`);
                    }
                } catch (error) {
                    console.log(`${colors.red.bold('Invalid input. Enter a number.')}`);
                }
            }

            while (true) {
                try {
                    const answer = await this.question(`${colors.yellow.bold('Max Delay For Each Tx -> ')}`);
                    const maxDelay = parseInt(answer.trim());
                    if (maxDelay >= this.min_delay) {
                        this.max_delay = maxDelay;
                        break;
                    } else {
                        console.log(`${colors.red.bold('Max Delay must be >= Min Delay.')}`);
                    }
                } catch (error) {
                    console.log(`${colors.red.bold('Invalid input. Enter a number.')}`);
                }
            }
        }

        // Proxy selection
        while (true) {
            try {
                console.log(`${colors.white.bold('1. Run With Free Proxyscrape Proxy')}`);
                console.log(`${colors.white.bold('2. Run With Private Proxy')}`);
                console.log(`${colors.white.bold('3. Run Without Proxy')}`);

                const answer = await this.question(`${colors.blue.bold('Choose [1/2/3] -> ')}`);
                choose = parseInt(answer.trim());

                if ([1, 2, 3].includes(choose)) {
                    const proxyType = choose === 1 ? "With Free Proxyscrape" :
                                     choose === 2 ? "With Private" :
                                     "Without";
                    console.log(`${colors.green.bold(`Run ${proxyType} Proxy Selected.`)}`);
                    break;
                } else {
                    console.log(`${colors.red.bold('Please enter either 1, 2 or 3.')}`);
                }
            } catch (error) {
                console.log(`${colors.red.bold('Invalid input. Enter a number (1, 2 or 3).')}`);
            }
        }

        rotate = false;
        if ([1, 2].includes(choose)) {
            while (true) {
                const answer = await this.question(`${colors.blue.bold('Rotate Invalid Proxy? [y/n] -> ')}`);
                const rotateAnswer = answer.trim();

                if (['y', 'n'].includes(rotateAnswer)) {
                    rotate = rotateAnswer === 'y';
                    break;
                } else {
                    console.log(`${colors.red.bold("Invalid input. Enter 'y' or 'n'.")}`);
                }
            }
        }

        return [option, choose, rotate];
    }

    async userLogin(privateKey, address, proxy = null, retries = 5) {
        const url = `${this.BASE_API}/users/login`;
        const payload = this.generatePayload(privateKey, address);
        const data = JSON.stringify(payload);

        const headers = {
            ...this.headers,
            "Content-Length": data.length.toString(),
            "Content-Type": "application/json"
        };

        await this.sleep(3000);

        for (let attempt = 0; attempt < retries; attempt++) {
            try {
                const config = {
                    method: 'POST',
                    url: url,
                    headers: headers,
                    data: data,
                    timeout: 120000
                };

                if (proxy) {
                    if (proxy.includes('socks')) {
                        config.httpsAgent = new SocksProxyAgent(proxy);
                        config.httpAgent = new SocksProxyAgent(proxy);
                    } else {
                        config.httpsAgent = new HttpsProxyAgent(proxy);
                        config.httpAgent = new HttpsProxyAgent(proxy);
                    }
                }

                const response = await axios(config);
                return response.data;
            } catch (error) {
                if (attempt < retries - 1) {
                    await this.sleep(5000);
                    continue;
                }
                this.log(`${colors.cyan.bold('Message   :')} ${colors.red.bold(error.message)}`);
            }
        }
        return null;
    }

    async checkEligibility(address, proxy = null, retries = 5) {
        const url = `${this.BASE_API}/faucet/check-eligibility`;
        const data = JSON.stringify({"token":"HLS", "chain":"helios-testnet"});

        const headers = {
            ...this.headers,
            "Authorization": `Bearer ${this.access_tokens[address]}`,
            "Content-Length": data.length.toString(),
            "Content-Type": "application/json"
        };

        await this.sleep(3000);

        for (let attempt = 0; attempt < retries; attempt++) {
            try {
                const config = {
                    method: 'POST',
                    url: url,
                    headers: headers,
                    data: data,
                    timeout: 120000
                };

                if (proxy) {
                    if (proxy.includes('socks')) {
                        config.httpsAgent = new SocksProxyAgent(proxy);
                        config.httpAgent = new SocksProxyAgent(proxy);
                    } else {
                        config.httpsAgent = new HttpsProxyAgent(proxy);
                        config.httpAgent = new HttpsProxyAgent(proxy);
                    }
                }

                const response = await axios(config);
                return response.data;
            } catch (error) {
                if (attempt < retries - 1) {
                    await this.sleep(5000);
                    continue;
                }
                this.log(`${colors.cyan.bold('Message   :')} ${colors.red.bold(error.message)}`);
            }
        }
        return null;
    }

    async requestFaucet(address, proxy = null, retries = 5) {
        const url = `${this.BASE_API}/faucet/request`;
        const data = JSON.stringify({"token":"HLS", "chain":"helios-testnet", "amount":1});

        const headers = {
            ...this.headers,
            "Authorization": `Bearer ${this.access_tokens[address]}`,
            "Content-Length": data.length.toString(),
            "Content-Type": "application/json"
        };

        await this.sleep(3000);

        for (let attempt = 0; attempt < retries; attempt++) {
            try {
                const config = {
                    method: 'POST',
                    url: url,
                    headers: headers,
                    data: data,
                    timeout: 120000
                };

                if (proxy) {
                    if (proxy.includes('socks')) {
                        config.httpsAgent = new SocksProxyAgent(proxy);
                        config.httpAgent = new SocksProxyAgent(proxy);
                    } else {
                        config.httpsAgent = new HttpsProxyAgent(proxy);
                        config.httpAgent = new HttpsProxyAgent(proxy);
                    }
                }

                const response = await axios(config);
                return response.data;
            } catch (error) {
                if (attempt < retries - 1) {
                    await this.sleep(5000);
                    continue;
                }
                this.log(`${colors.cyan.bold('Message   :')} ${colors.red.bold(error.message)}`);
            }
        }
        return null;
    }

    async processUserLogin(privateKey, address, useProxy, rotateProxy) {
        while (true) {
            const proxy = useProxy ? this.getNextProxyForAccount(address) : null;
            this.log(`${colors.cyan.bold('Proxy     :')} ${colors.white.bold(proxy || 'None')}`);

            const login = await this.userLogin(privateKey, address, proxy);
            if (login && login.success) {
                this.access_tokens[address] = login.token;
                this.log(`${colors.cyan.bold('Status    :')} ${colors.green.bold('Login Success')}`);
                return true;
            }

            if (rotateProxy) {
                this.log(`${colors.cyan.bold('Status    :')} ${colors.red.bold('Login Failed,')} ${colors.yellow.bold('Rotating Proxy...')}`);
                this.rotateProxyForAccount(address);
                await this.sleep(5000);
                continue;
            }

            this.log(`${colors.cyan.bold('Status    :')} ${colors.red.bold('Login Failed')}`);
            return false;
        }
    }

    async processPerformBridge(privateKey, address, useProxy) {
        const [txHash, blockNumber] = await this.performBridge(privateKey, address, useProxy);
        if (txHash && blockNumber) {
            const explorer = `https://explorer.helioschainlabs.org/tx/${txHash}`;
            this.log(`${colors.cyan.bold('   Status   :')} ${colors.green.bold('Success')}`);
            this.log(`${colors.cyan.bold('   Block    :')} ${colors.white.bold(blockNumber)}`);
            this.log(`${colors.cyan.bold('   Tx Hash  :')} ${colors.white.bold(txHash)}`);
            this.log(`${colors.cyan.bold('   Explorer :')} ${colors.white.bold(explorer)}`);
        } else {
            this.log(`${colors.cyan.bold('   Status   :')} ${colors.red.bold('Perform On-Chain Failed')}`);
        }
    }

    async processPerformDelegate(privateKey, address, validationAddress, useProxy) {
        const [txHash, blockNumber] = await this.performDelegate(privateKey, address, validationAddress, useProxy);
        if (txHash && blockNumber) {
            const explorer = `https://explorer.helioschainlabs.org/tx/${txHash}`;
            this.log(`${colors.cyan.bold('   Status   :')} ${colors.green.bold('Success')}`);
            this.log(`${colors.cyan.bold('   Block    :')} ${colors.white.bold(blockNumber)}`);
            this.log(`${colors.cyan.bold('   Tx Hash  :')} ${colors.white.bold(txHash)}`);
            this.log(`${colors.cyan.bold('   Explorer :')} ${colors.white.bold(explorer)}`);
        } else {
            this.log(`${colors.cyan.bold('   Status   :')} ${colors.red.bold('Perform On-Chain Failed')}`);
        }
    }

    async processOption1(address, useProxy) {
        const proxy = useProxy ? this.getNextProxyForAccount(address) : null;

        const check = await this.checkEligibility(address, proxy);
        if (check && check.success) {
            const isEligible = check.isEligible;

            if (isEligible) {
                const request = await this.requestFaucet(address, proxy);
                if (request && request.success) {
                    this.log(`${colors.cyan.bold('Faucet    :')} ${colors.green.bold('1 HLS Claimed Successfully')}`);
                }
            } else {
                this.log(`${colors.cyan.bold('Faucet    :')} ${colors.yellow.bold('Not Eligible to Claim')}`);
            }
        }
    }

    async processOption2(privateKey, address, useProxy) {
        this.log(`${colors.cyan.bold('Bridge    :')}`);

        for (let i = 0; i < this.bridge_count; i++) {
            this.log(`${colors.green.bold(' ● ')}${colors.white.bold(i + 1)}${colors.magenta.bold(' Of ')}${colors.white.bold(this.bridge_count)}`);

            const balance = await this.getTokenBalance(address, "HLS", useProxy);

            this.log(`${colors.cyan.bold('   Balance  :')} ${colors.white.bold(`${balance} HLS`)}`);
            this.log(`${colors.cyan.bold('   Amount   :')} ${colors.white.bold(`${this.bridge_amount} HLS`)}`);

            if (!balance || balance <= this.bridge_amount) {
                this.log(`${colors.cyan.bold('   Status   :')} ${colors.yellow.bold('Insufficient HLS Token Balance')}`);
                return;
            }

            await this.processPerformBridge(privateKey, address, useProxy);
            await this.printTimer();
        }
    }

    async processOption3(privateKey, address, useProxy) {
        this.log(`${colors.cyan.bold('Delegate  :')}`);

        for (let i = 0; i < this.delegate_count; i++) {
            this.log(`${colors.green.bold(' ● ')}${colors.white.bold(i + 1)}${colors.magenta.bold(' Of ')}${colors.white.bold(this.delegate_count)}`);

            const validationAddress = Math.random() < 0.5 ? this.HLS_HEDGE_VALIDATION : this.HLS_SUPRA_VALIDATION;
            const validatorName = validationAddress === this.HLS_HEDGE_VALIDATION ? "Helios-Hedge" : "Helios-Supra";

            const balance = await this.getTokenBalance(address, "HLS", useProxy);

            this.log(`${colors.cyan.bold('   Balance  :')} ${colors.white.bold(`${balance} HLS`)}`);
            this.log(`${colors.cyan.bold('   Amount   :')} ${colors.white.bold(`${this.delegate_amount} HLS`)}`);
            this.log(`${colors.cyan.bold('   Validator:')} ${colors.white.bold(validatorName)}`);

            if (!balance || balance <= this.delegate_amount) {
                this.log(`${colors.cyan.bold('   Status   :')} ${colors.yellow.bold('Insufficient HLS Token Balance')}`);
                return;
            }

            await this.processPerformDelegate(privateKey, address, validationAddress, useProxy);
            await this.printTimer();
        }
    }

    async processAccounts(privateKey, address, option, useProxy, rotateProxy) {
        const logined = await this.processUserLogin(privateKey, address, useProxy, rotateProxy);
        if (logined) {
            if (option === 1) {
                await this.processOption1(address, useProxy);
            } else if (option === 2) {
                await this.processOption2(privateKey, address, useProxy);
            } else if (option === 3) {
                await this.processOption3(privateKey, address, useProxy);
            } else {
                await this.processOption1(address, useProxy);
                await this.sleep(5000);

                await this.processOption2(privateKey, address, useProxy);
                await this.sleep(5000);

                await this.processOption3(privateKey, address, useProxy);
                await this.sleep(5000);
            }
        }
    }

    async main() {
        try {
            // Read accounts from file
            let accounts;
            try {
                const content = fs.readFileSync('accounts.txt', 'utf8');
                accounts = content.split('\n').filter(line => line.trim());
            } catch (error) {
                this.log(`${colors.red.bold('File \'accounts.txt\' Not Found.')}`);
                return;
            }

            const [option, useProxyChoice, rotateProxy] = await this.printQuestion();

            const useProxy = [1, 2].includes(useProxyChoice);

            while (true) {
                this.clearTerminal();
                this.welcome();
                this.log(`${colors.green.bold('Account\'s Total:')} ${colors.white.bold(accounts.length)}`);

                if (useProxy) {
                    await this.loadProxies(useProxyChoice);
                }

                const separator = "=".repeat(25);
                for (const account of accounts) {
                    if (account) {
                        const address = this.generateAddress(account);

                        this.log(`${colors.cyan.bold(separator)}${colors.cyan.bold('[')}${colors.white.bold(` ${this.maskAccount(address)} `)}${colors.cyan.bold(']')}${colors.cyan.bold(separator)}`);

                        if (!address) {
                            this.log(`${colors.cyan.bold('Status    :')} ${colors.red.bold('Invalid Private Key or Library Version Not Supported')}`);
                            continue;
                        }

                        await this.processAccounts(account, address, option, useProxyChoice, rotateProxy);
                        await this.sleep(3000);
                    }
                }

                this.log(`${colors.cyan.bold('=')}`.repeat(72));
                let seconds = 24 * 60 * 60;
                while (seconds > 0) {
                    const formattedTime = this.formatSeconds(seconds);
                    process.stdout.write(`${colors.cyan.bold('[ Wait for')} ${colors.white.bold(formattedTime)} ${colors.cyan.bold('... ]')} ${colors.white.bold('|')} ${colors.blue.bold('All Accounts Have Been Processed.')}\r`);
                    await this.sleep(1000);
                    seconds--;
                }
            }
        } catch (error) {
            if (error.code === 'ENOENT') {
                this.log(`${colors.red.bold('File \'accounts.txt\' Not Found.')}`);
                return;
            }
            this.log(`${colors.red.bold(`Error: ${error.message}`)}`);
            throw error;
        }
    }
}

export default Helios;
