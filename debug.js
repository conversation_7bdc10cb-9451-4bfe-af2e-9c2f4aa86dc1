import Helios from './bot.js';

async function testLogin() {
    console.log('=== TESTING LOGIN PROCESS ===');
    
    try {
        const bot = new Helios();
        
        // Test private key
        const privateKey = "b00437e327b08ef5fe33c30fab9355309a3ca047f9c6be28dccdc91860f7ed5a";
        console.log('Private Key:', privateKey);
        
        // Test address generation
        const address = bot.generateAddress(privateKey);
        console.log('Generated Address:', address);
        
        if (!address) {
            console.error('Failed to generate address');
            return;
        }
        
        // Test payload generation
        console.log('\n=== TESTING PAYLOAD GENERATION ===');
        const payload = await bot.generatePayload(privateKey, address);
        console.log('Generated Payload:', payload);
        
        if (!payload) {
            console.error('Failed to generate payload');
            return;
        }
        
        // Test login
        console.log('\n=== TESTING LOGIN ===');
        const loginResult = await bot.userLogin(privateKey, address, null);
        console.log('Login Result:', loginResult);
        
        if (loginResult && loginResult.success) {
            console.log('✅ Login successful!');
            console.log('Token:', loginResult.token);

            // Save token for further testing
            bot.access_tokens[address] = loginResult.token;

            // Test faucet claim
            console.log('\n=== TESTING FAUCET CLAIM ===');
            const eligibilityCheck = await bot.checkEligibility(address, null);
            console.log('Eligibility Check:', eligibilityCheck);

            if (eligibilityCheck && eligibilityCheck.success) {
                if (eligibilityCheck.isEligible) {
                    console.log('✅ Eligible for faucet');
                    const faucetResult = await bot.requestFaucet(address, null);
                    console.log('Faucet Result:', faucetResult);

                    if (faucetResult && faucetResult.success) {
                        console.log('✅ Faucet claim successful!');
                    } else {
                        console.log('❌ Faucet claim failed');
                    }
                } else {
                    console.log('⚠️ Not eligible for faucet');
                }
            } else {
                console.log('❌ Failed to check eligibility');
            }
        } else {
            console.log('❌ Login failed');
        }
        
    } catch (error) {
        console.error('Error in test:', error.message);
        console.error('Stack:', error.stack);
    }
}

testLogin();
