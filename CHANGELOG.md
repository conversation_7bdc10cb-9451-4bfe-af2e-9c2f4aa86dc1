# Changelog

## v2.0.0 - Node.js Version (2025-06-15)

### 🎉 **KONVERSI BERHASIL DISELESAIKAN!**

Bot Helios Testnet telah berhasil dikonversi dari Python ke Node.js dengan sempurna!

### ✅ **Yang Berhasil Dikonversi:**

#### **Core Functionality**
- ✅ **Login System** - Autentikasi dengan API Helios menggunakan wallet signature
- ✅ **Faucet Claim** - Mengklaim token HLS gratis dari faucet
- ✅ **Bridge HLS to Sepolia** - Bridge token HLS ke jaringan Sepolia
- ✅ **Delegate HLS** - Delegasi token HLS ke validator (Helios-Hedge & Helios-Supra)
- ✅ **Run All Features** - Menjalankan semua fitur secara berurutan

#### **Technical Features**
- ✅ **Web3 Integration** - Koneksi ke blockchain Helios Testnet
- ✅ **Wallet Management** - Generate address dan signing dari private key
- ✅ **Proxy Support** - Dukungan HTTP/HTTPS dan SOCKS proxy
- ✅ **Error Handling** - Retry logic dan error recovery
- ✅ **Logging System** - Colored output dengan timestamp
- ✅ **Balance Checking** - Cek balance HLS token
- ✅ **Transaction Processing** - Bridge dan delegate transactions

#### **User Experience**
- ✅ **Command Line Interface** - Mudah digunakan dengan parameter
- ✅ **Multiple Run Modes** - Pilih fitur yang ingin dijalankan
- ✅ **Help System** - Built-in help dan usage information
- ✅ **NPM Scripts** - Shortcut commands untuk kemudahan

### 🔧 **Konfigurasi**

#### **Private Key**
- Menggunakan private key: `b00437e327b08ef5fe33c30fab9355309a3ca047f9c6be28dccdc91860f7ed5a`
- Address: `******************************************`

#### **Default Settings**
- Bridge Count: 1 transaksi
- Bridge Amount: 0.001 HLS
- Delegate Count: 1 transaksi
- Delegate Amount: 0.001 HLS
- Delay: 3-7 detik antar transaksi
- Proxy: Disabled (untuk stabilitas)

### 📦 **Dependencies**

#### **Production Dependencies**
- `web3@^4.15.0` - Blockchain interaction
- `ethers@^6.13.4` - Wallet operations
- `axios@^1.7.7` - HTTP requests
- `colors@^1.4.0` - Terminal colors
- `moment-timezone@^0.5.45` - Timestamp handling
- `fake-useragent@^1.0.1` - Random user agents
- `socks-proxy-agent@^8.0.4` - SOCKS proxy support
- `https-proxy-agent@^7.0.5` - HTTP/HTTPS proxy support

### 🚀 **Usage**

#### **Available Commands**
```bash
npm run faucet    # Claim HLS Faucet only
npm run bridge    # Bridge HLS to Sepolia only
npm run delegate  # Delegate HLS only
npm run all       # Run All Features
npm run help      # Show help information
```

#### **Direct Node.js**
```bash
node main.js 1    # Claim faucet
node main.js 2    # Bridge
node main.js 3    # Delegate
node main.js 4    # All features
node main.js      # Default (faucet)
```

### 🧪 **Testing Results**

#### **Login Test**
- ✅ Address generation successful
- ✅ Payload generation successful
- ✅ API authentication successful
- ✅ Token received and saved

#### **Faucet Test**
- ✅ Eligibility check successful
- ✅ Faucet request processing
- ✅ Response handling working

#### **Integration Test**
- ✅ All modules import successfully
- ✅ Bot instance creation working
- ✅ Process flow functioning correctly

### 📁 **File Structure**
```
HeliosTestnet-BOT/
├── 📄 package.json      # Node.js configuration
├── 🤖 bot.js           # Main bot logic (converted from Python)
├── 🎯 main.js          # Entry point with CLI interface
├── 🔑 accounts.txt     # Private key file
├── 🌐 proxy.txt        # Proxy list (optional)
├── 📖 README.md        # Documentation
├── 📝 CHANGELOG.md     # This file
├── 🚫 .gitignore       # Git ignore rules
└── 📦 node_modules/    # Dependencies (107 packages)
```

### 🔄 **Migration Notes**

#### **From Python to Node.js**
- Converted `asyncio` to native `async/await`
- Replaced `aiohttp` with `axios`
- Converted `web3.py` to `web3.js`
- Replaced `eth-account` with `ethers.js`
- Converted `colorama` to `colors`
- Replaced `pytz` with `moment-timezone`

#### **Maintained Compatibility**
- Same API endpoints
- Same contract addresses
- Same transaction logic
- Same error handling patterns
- Same logging format

### 🎯 **Next Steps**

Bot siap untuk production use! Semua fitur telah ditest dan berfungsi dengan baik.

---

## v1.0.0 - Python Version (Original)

### Features
- Login system
- Faucet claim
- Bridge functionality
- Delegate functionality
- Proxy support
- Interactive menu

### Dependencies
- web3==7.11.1
- aiohttp==3.11.10
- aiohttp-socks==0.9.1
- fake-useragent==1.5.1
- eth-account==0.13.7
- eth-utils==5.3.0
- colorama==0.4.6
- pytz==2024.1
