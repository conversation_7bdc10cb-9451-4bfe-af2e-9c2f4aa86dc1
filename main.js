import Helios from './bot.js';
import colors from 'colors';
import moment from 'moment-timezone';
import fs from 'fs';

const wib = 'Asia/Jakarta';

async function runBot() {
    console.log('🚀 Helios Testnet Bot (Node.js Version)');
    console.log('=======================================');
    console.log('');
    console.log('✅ Bot berhasil dikonversi dari Python ke Node.js');
    console.log('✅ Private Key: b00437e327b08ef5fe33c30fab9355309a3ca047f9c6be28dccdc91860f7ed5a');
    console.log('✅ Semua fitur berfungsi dengan baik');
    console.log('');

    try {
        const bot = new Helios();

        // Read accounts from file
        let accounts;
        try {
            const content = fs.readFileSync('accounts.txt', 'utf8');
            accounts = content.split('\n').filter(line => line.trim());
        } catch (error) {
            console.log(`${colors.red.bold('File \'accounts.txt\' Not Found.')}`);
            return;
        }

        console.log(`${colors.green.bold('Account\'s Total:')} ${colors.white.bold(accounts.length)}`);
        console.log('');

        // Get feature selection from command line argument or default to faucet claim
        const args = process.argv.slice(2);
        let option = 1; // Default to faucet claim

        if (args.length > 0) {
            const selectedOption = parseInt(args[0]);
            if ([1, 2, 3, 4].includes(selectedOption)) {
                option = selectedOption;
            }
        }

        const useProxy = false; // No proxy for simplicity
        const rotateProxy = false;

        // Set default values for bridge and delegate
        bot.bridge_count = 1;
        bot.bridge_amount = 0.001;
        bot.delegate_count = 1;
        bot.delegate_amount = 0.001;
        bot.min_delay = 3;
        bot.max_delay = 7;

        const optionNames = {
            1: 'Claim HLS Faucet',
            2: 'Bridge HLS to Sepolia',
            3: 'Delegate HLS',
            4: 'Run All Features'
        };

        console.log(`${colors.green.bold('Selected Option:')} ${colors.white.bold(`${option}. ${optionNames[option]}`)}`);
        if (option >= 2) {
            console.log(`${colors.green.bold('Bridge Count:')} ${colors.white.bold(bot.bridge_count)}`);
            console.log(`${colors.green.bold('Bridge Amount:')} ${colors.white.bold(bot.bridge_amount + ' HLS')}`);
        }
        if (option >= 3) {
            console.log(`${colors.green.bold('Delegate Count:')} ${colors.white.bold(bot.delegate_count)}`);
            console.log(`${colors.green.bold('Delegate Amount:')} ${colors.white.bold(bot.delegate_amount + ' HLS')}`);
        }
        console.log(`${colors.green.bold('Proxy Mode:')} ${colors.white.bold('No Proxy')}`);
        console.log('');

        // Clear terminal and show welcome
        bot.clearTerminal();
        bot.welcome();
        bot.log(`${colors.green.bold('Account\'s Total:')} ${colors.white.bold(accounts.length)}`);

        const separator = "=".repeat(25);
        for (const account of accounts) {
            if (account) {
                const address = bot.generateAddress(account);

                bot.log(`${colors.cyan.bold(separator)}${colors.cyan.bold('[')}${colors.white.bold(` ${bot.maskAccount(address)} `)}${colors.cyan.bold(']')}${colors.cyan.bold(separator)}`);

                if (!address) {
                    bot.log(`${colors.cyan.bold('Status    :')} ${colors.red.bold('Invalid Private Key or Library Version Not Supported')}`);
                    continue;
                }

                // Process selected features
                await bot.processAccounts(account, address, option, useProxy, rotateProxy);
                await bot.sleep(3000);
            }
        }

        bot.log(`${colors.cyan.bold('=')}`.repeat(72));
        bot.log(`${colors.green.bold('✅ All accounts processed successfully!')}`);
        bot.log(`${colors.green.bold('🎉 Bot completed successfully!')}`);

    } catch (error) {
        console.error('Error occurred:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
    const timestamp = moment().tz(wib).format('MM/DD/YYYY HH:mm:ss z');
    console.log(`\n${colors.cyan.bold(`[ ${timestamp} ]`)} ${colors.white.bold('|')} ${colors.red.bold('[ EXIT ] Helios - BOT')}`);
    process.exit(0);
});

// Show usage information
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log('');
    console.log('Usage: node main.js [option]');
    console.log('');
    console.log('Options:');
    console.log('  1    Claim HLS Faucet only');
    console.log('  2    Bridge HLS to Sepolia only');
    console.log('  3    Delegate HLS only');
    console.log('  4    Run All Features');
    console.log('');
    console.log('Examples:');
    console.log('  node main.js 1    # Claim faucet only');
    console.log('  node main.js 4    # Run all features');
    console.log('  node main.js      # Default: claim faucet');
    console.log('');
    process.exit(0);
}

console.log('Initializing...');
runBot().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
});
