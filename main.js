import Helios from './bot.js';
import colors from 'colors';
import moment from 'moment-timezone';

const wib = 'Asia/Jakarta';

async function main() {
    console.log('Starting Helios Bot...');

    try {
        const bot = new Helios();
        console.log('Bot instance created successfully');

        await bot.main();
    } catch (error) {
        if (error.name === 'KeyboardInterrupt' || error.code === 'SIGINT') {
            const timestamp = moment().tz(wib).format('MM/DD/YYYY HH:mm:ss z');
            console.log(`${colors.cyan.bold(`[ ${timestamp} ]`)} ${colors.white.bold('|')} ${colors.red.bold('[ EXIT ] Helios - BOT')}`);
        } else {
            console.error('Error occurred:', error.message);
            console.error('Stack trace:', error.stack);
        }
    }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
    const timestamp = moment().tz(wib).format('MM/DD/YYYY HH:mm:ss z');
    console.log(`\n${colors.cyan.bold(`[ ${timestamp} ]`)} ${colors.white.bold('|')} ${colors.red.bold('[ EXIT ] Helios - BOT')}`);
    process.exit(0);
});

console.log('Initializing...');
main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
});
