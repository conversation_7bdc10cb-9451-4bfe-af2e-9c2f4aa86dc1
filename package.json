{"name": "helios-testnet-bot", "version": "1.0.0", "description": "<PERSON><PERSON><PERSON> converted to Node.js", "main": "bot.js", "type": "module", "scripts": {"start": "node main.js", "faucet": "node main.js 1", "bridge": "node main.js 2", "delegate": "node main.js 3", "all": "node main.js 4", "help": "node main.js --help"}, "dependencies": {"web3": "^4.15.0", "axios": "^1.7.7", "ethers": "^6.13.4", "colors": "^1.4.0", "moment-timezone": "^0.5.45", "fake-useragent": "^1.0.1", "socks-proxy-agent": "^8.0.4", "https-proxy-agent": "^7.0.5"}, "keywords": ["helios", "testnet", "bot", "blockchain", "crypto"], "author": "", "license": "MIT"}