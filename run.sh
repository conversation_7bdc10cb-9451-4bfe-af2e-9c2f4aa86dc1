#!/bin/bash

echo "🚀 Starting Helios Testnet Bot (Node.js Version)"
echo "================================================"
echo ""
echo "Private Key: b00437e327b08ef5fe33c30fab9355309a3ca047f9c6be28dccdc91860f7ed5a"
echo ""
echo "Available Options:"
echo "1. Claim HLS Faucet"
echo "2. Bridge HLS to Sepolia"
echo "3. Delegate HLS"
echo "4. Run All Features"
echo ""
echo "Proxy Options:"
echo "1. Run With Free Proxyscrape Proxy"
echo "2. Run With Private Proxy"
echo "3. Run Without Proxy"
echo ""
echo "Starting bot..."
echo "================================================"
echo ""

# Run the bot
node main.js
